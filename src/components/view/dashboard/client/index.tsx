"use client";

import React, { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Separator } from "@/components/common/ui/separator";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
  TabsContent,
} from "@/components/common/ui/tabs";
import {
  ExternalLink,
  BookmarkIcon,
  Search,
  Clock,
  MapPin,
  Package,
  Shield,
  Loader2,
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useTenders } from "@/hooks/useTenders";
import { Listing } from "@/layouts/dashboard/details/basic";
import type { TenderResponse } from "@/store/actions/tender";

// Helper function to transform tender data for WideCard
const transformTenderToWideCard = (tender: TenderResponse) => {
  // Extract location from procuring entity or address
  const getLocation = () => {
    if (tender.procuring_entity) {
      // Extract location from procuring entity name (e.g., "TARURA - DAR ES SALAAM REGIONAL OFFICE")
      const match = tender.procuring_entity.match(/- (.+?) REGIONAL/);
      if (match) {
        return match[1].toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase());
      }
    }
    return "Location TBD";
  };

  // Format creation date
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} days ago`;
    return date.toLocaleDateString();
  };

  // Determine if tender is accessible (for demo purposes, make some locked)
  const isAccessible = tender.status === "active" || tender.status === "online";

  const tags = [
    {
      label: tender.category || "General",
      variant: "secondary" as const,
      icon: Package,
    },
    {
      label: getLocation(),
      variant: "outline" as const,
      icon: MapPin,
    },
    {
      label: formatTimeAgo(tender.createdAt),
      variant: "outline" as const,
      icon: Clock,
    },
  ];

  // Add status tag
  if (tender.status) {
    tags.push({
      label: tender.status.charAt(0).toUpperCase() + tender.status.slice(1),
      variant:
        tender.status === "active"
          ? ("secondary" as const)
          : ("outline" as const),
      icon: tender.status === "active" ? Package : Clock,
    });
  }

  const actions = (
    <div className="flex items-center gap-2">
      {isAccessible ? (
        <div className="flex items-center gap-2">
          <Button>
            <ExternalLink />
            Open
          </Button>
          <Button variant="outline">
            <BookmarkIcon />
          </Button>
        </div>
      ) : (
        <Button variant="outline">Unlock to View</Button>
      )}
    </div>
  );

  return {
    id: tender.id,
    subHeading: tender.procuring_entity || "Procurement Entity",
    title: tender.description || "Tender Opportunity",
    description:
      tender.description ||
      "No description available for this tender opportunity.",
    tags,
    actions,
  };
};

export function ClientDashboardContainer() {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("recent");
  const { user } = useAuth();

  // Initialize tenders hook with pagination
  const { tenders, isLoading, error, search, clearSearch, isSearching } =
    useTenders({
      page: 1,
      limit: 10,
      sortBy: "createdAt",
      sortOrder: "desc",
    });

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 17) return "Good afternoon";
    return "Good evening";
  };

  // Handle search functionality
  const handleSearch = async (query: string) => {
    if (query.trim()) {
      await search(query);
    } else {
      clearSearch();
    }
  };

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      handleSearch(searchTerm);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Transform tenders data for WideCard
  const transformedTenders = useMemo(() => {
    return tenders.map(transformTenderToWideCard);
  }, [tenders]);

  return (
    <div className="min-h-screen bg-background relative">
      {/* Header */}
      <div className="flex items-center justify-between p-6 pb-4">
        <div>
          <h1 className="text-2xl font-semibold text-foreground">
            {getGreeting()} {user?.firstName} {user?.lastName?.charAt(0)}.
          </h1>
        </div>
        <div className="relative w-80">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="px-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="w-max">
            <TabsTrigger value="recent">Recent</TabsTrigger>
            <TabsTrigger value="saved">Saved</TabsTrigger>
            <TabsTrigger value="viewed">Viewed</TabsTrigger>
          </TabsList>

          <Separator />

          {/* Tab Content */}
          <TabsContent value="recent" className="mt-6">
            {isLoading || isSearching ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center space-y-4">
                  <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
                  <p className="text-muted-foreground">
                    {isSearching
                      ? "Searching tenders..."
                      : "Loading tenders..."}
                  </p>
                </div>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <p className="text-destructive mb-4">Failed to load tenders</p>
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                >
                  Try Again
                </Button>
              </div>
            ) : transformedTenders.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  {searchTerm
                    ? "No tenders match your search"
                    : "No tenders available"}
                </p>
                {searchTerm && (
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm("");
                      clearSearch();
                    }}
                    className="mt-2"
                  >
                    Clear Search
                  </Button>
                )}
              </div>
            ) : (
              <div className="grid gap-6">
                {transformedTenders.map((tender) => (
                  <Listing.WideCard
                    key={tender.id}
                    subHeading={tender.subHeading}
                    title={tender.title}
                    description={tender.description}
                    tags={tender.tags}
                    actions={tender.actions}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="saved" className="mt-6">
            <div className="text-center py-12">
              <p className="text-muted-foreground">No saved items yet</p>
            </div>
          </TabsContent>

          <TabsContent value="viewed" className="mt-6">
            <div className="text-center py-12">
              <p className="text-muted-foreground">No viewed items yet</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Upgrade Banner */}
      <div className="absolute bottom-0 left-0 right-0 bg-green-100 border-t border-green-200 p-4">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-3">
            <div className="bg-green-600 p-2 rounded">
              <Shield className="h-5 w-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-green-800">Upgrade to Quick Win</p>
              <p className="text-sm text-green-600">
                Unlock 200+ features, integrations, and advanced reporting.
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="border-green-600 text-green-600 hover:bg-green-50"
            >
              Upgrade Now
            </Button>
            <Button variant="ghost" size="sm" className="text-green-600">
              ×
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
