"use client";

import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { ArrowLeft, Bookmark, BookmarkCheck } from "lucide-react";
import type { TenderResponse } from "@/store/actions/tender";

interface TenderDetailsHeaderProps {
  tender: TenderResponse;
  isBookmarked: boolean;
  onBack: () => void;
  onBookmark: () => void;
  onDoItForMe: () => void;
}

export function TenderDetailsHeader({
  tender,
  isBookmarked,
  onBack,
  onBookmark,
  onDoItForMe,
}: TenderDetailsHeaderProps) {
  return (
    <div className="space-y-4">
      {/* Back button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={onBack}
        className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
      >
        <ArrowLeft className="h-4 w-4" />
        Back
      </Button>

      {/* Header content */}
      <div className="flex items-start justify-between gap-4">
        <div className="flex-1 space-y-2">
          <h1 className="text-2xl font-bold text-foreground">
            {tender.description || "Tender Details"}
          </h1>
          <p className="text-muted-foreground">
            {tender.procuring_entity || "Procuring Entity"}
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onBookmark}
            className="flex items-center gap-2"
          >
            {isBookmarked ? (
              <BookmarkCheck className="h-4 w-4" />
            ) : (
              <Bookmark className="h-4 w-4" />
            )}
          </Button>

          <Button
            onClick={onDoItForMe}
            className="bg-emerald-600 hover:bg-emerald-700 text-white"
          >
            DO IT FOR ME
          </Button>
        </div>
      </div>
    </div>
  );
}
