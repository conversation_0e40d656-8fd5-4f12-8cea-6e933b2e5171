"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useTender } from "@/hooks/useTenders";
import { TenderDetailsHeader } from "./header";
import { TenderDetailsContent } from "./content";
import { Card, CardContent } from "@/components/common/ui/card";
import { Skeleton } from "@/components/common/ui/skeleton";
import { toast } from "sonner";
import type { TenderResponse } from "@/store/actions/tender";

interface TenderDetailsContainerProps {
  tenderId: string;
}

export function TenderDetailsContainer({
  tenderId,
}: TenderDetailsContainerProps) {
  const router = useRouter();
  const { tender, error, isLoading, refresh } = useTender(tenderId);
  const [isBookmarked, setIsBookmarked] = useState(false);

  useEffect(() => {
    if (error) {
      console.error("Tender details error:", error);
      toast.error("Failed to load tender details");
    }
  }, [error]);

  const handleBack = () => {
    router.back();
  };

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked);
    toast.success(
      isBookmarked ? "Removed from bookmarks" : "Added to bookmarks"
    );
  };

  const handleDoItForMe = () => {
    // TODO: Implement "DO IT FOR ME" functionality
    toast.info("Feature coming soon!");
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-64" />
          <div className="flex gap-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !tender) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">
              {error ? "Failed to load tender details" : "Tender not found"}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <TenderDetailsHeader
        tender={tender}
        isBookmarked={isBookmarked}
        onBack={handleBack}
        onBookmark={handleBookmark}
        onDoItForMe={handleDoItForMe}
      />
      <TenderDetailsContent tender={tender} />
    </div>
  );
}
